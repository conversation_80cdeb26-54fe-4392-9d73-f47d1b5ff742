<template>
  <div class="main-class">
    <div>
      <el-form class="searchClass" @submit.prevent="handleSearch">
        <el-form-item label="教材名称" class="searchItem">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入教材名称"
            class="mr-4"
          >
          </el-input>
        </el-form-item>
        <el-form-item class="searchItem">
          <el-button type="primary" @click="handleSearch">
            <el-icon><search /></el-icon>
            搜索
          </el-button>
        </el-form-item>
        <el-form-item class="searchItem">
          <el-button type="warning" @click="handleAdd">新增教材</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="tableData" style="width: 100%">
      <el-table-column fixed prop="name" label="教材名称" width="350" />
      <el-table-column prop="typeName" label="教材类型" width="120" />
      <el-table-column prop="stage" label="阶段" width="100" />
      <el-table-column prop="gradeName" label="年级" width="200" />
      <el-table-column prop="semesterName" label="学期" width="200" />
      <el-table-column prop="publisher" label="版本" width="200" />
      <el-table-column prop="required" label="必修" width="200" />
      <el-table-column prop="statUnitCnt" label="单元数量" width="100" />
      <el-table-column prop="statWordCnt" label="单词数量" width="100" />
      <el-table-column fixed="right" label="操作" min-width="180">
        <template #default="scope">
          <el-button
            link
            type="primary"
            size="small"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            link
            type="success"
            size="small"
            @click="handleDownloadHandout(scope.row)"
          >
            下载讲义
          </el-button>
          <el-button
            link
            type="warning"
            size="small"
            @click="handleDownloadAudio(scope.row)"
          >
            下载音频
          </el-button>
          <el-button
            link
            type="info"
            size="small"
            @click="handleDownloadExercise(scope.row)"
          >
            下载练习
          </el-button>
          <el-button
            link
            type="primary"
            size="small"
            @click="deletedText(scope.row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      v-model="dialogVisible"
      title="新增教材"
      width="60%"
      :before-close="cancelForm"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
        <div class="dialog-content">
          <div class="dialog-top">
            <div class="cover-upload">
              <el-upload
                class="avatar-uploader"
                action="#"
                accept="image/*"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handleCoverChange"
              >
                <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
              </el-upload>
              <div class="upload-text">选择教材封面</div>
            </div>
            <div class="form-section">
              <el-form-item label="教材名称" prop="name">
                <el-input
                  v-model="form.name"
                  placeholder="请输入教材名称"
                ></el-input>
              </el-form-item>
              <el-form-item label="教材类型" prop="type">
                <el-select
                  v-model="form.type"
                  placeholder="请选择教材类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dictTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="教材版本" prop="publisher">
                <el-input
                  v-model="form.publisher"
                  placeholder="请输入教材版本"
                ></el-input>
              </el-form-item>
              <el-form-item label="年级" prop="grade">
                <el-select
                  v-model="form.grade"
                  placeholder="请选择年级"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in gradeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="学期" prop="semester">
                <el-select
                  v-model="form.semester"
                  placeholder="请选择学期"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in semesterOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="阶段" prop="stage">
                <el-select
                  v-model="form.stage"
                  placeholder="请选择阶段"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in stageOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="必修">
                <el-input
                  v-model="form.required"
                  placeholder="请输入必修"
                ></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="dialog-bottom">
            <div class="content-label">
              <span style="margin-right: 10px">教材内容</span>
              <el-tooltip effect="dark" placement="top">
                <template #content>
                  <div>
                    请按照格式输入教材内容，用>表示单元名称，特色教材和学生教材无需>。例子如下：<br />
                    学校教材案例：<br />
                    >unit-1<br />
                    Apple<br />
                    Banana<br />
                    >unit-2<br />
                    特色教材和学生教材案例：<br />
                    Apple<br />
                    Banana<br />
                    Cat
                  </div>
                </template>
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
            <el-form-item prop="content">
              <el-input
                v-model="form.content"
                type="textarea"
                :rows="10"
                placeholder="教材格式为：用>表示单元名称，特色教材和学生教材无需>。例子如下：
             学校教材案例：
             >unit-1
             Apple
             Banana
             >unit-2
             特色教材和学生教材案例：
             Apple
             Banana
             Cat"
              ></el-input>
            </el-form-item>
          </div>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelForm">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 下载讲义对话框 -->
    <el-dialog
      v-model="downloadDialogVisible"
      title="下载讲义"
      width="500px"
      :before-close="cancelDownload"
    >
      <div class="download-content">
        <div class="textbook-info">
          <h4>教材信息</h4>
          <p><strong>教材名称：</strong>{{ currentTextbook?.name }}</p>
          <p><strong>单词数量：</strong>{{ currentTextbook?.statWordCnt }}个</p>
        </div>

        <el-form :model="downloadForm" label-width="100px">
          <el-form-item label="下载方式">
            <el-radio-group v-model="downloadForm.downloadType">
              <el-radio label="whole">整本下载</el-radio>
              <el-radio label="range">自选范围</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            v-if="downloadForm.downloadType === 'range'"
            label="单词范围"
          >
            <div class="range-inputs">
              <span>从第</span>
              <el-input-number
                v-model="downloadForm.startWordIndex"
                :min="1"
                :max="currentTextbook?.statWordCnt || 1"
                size="small"
                style="width: 80px; margin: 0 8px;"
              />
              <span>个单词到第</span>
              <el-input-number
                v-model="downloadForm.endWordIndex"
                :min="downloadForm.startWordIndex || 1"
                :max="currentTextbook?.statWordCnt || 1"
                size="small"
                style="width: 80px; margin: 0 8px;"
              />
              <span>个单词</span>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelDownload">取消</el-button>
          <el-button
            type="primary"
            @click="confirmDownload"
            :loading="downloadLoading"
          >
            确定下载
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 下载音频对话框 -->
    <el-dialog
      v-model="downloadAudioDialogVisible"
      title="下载音频"
      width="500px"
      :before-close="cancelDownloadAudio"
    >
      <div class="download-content">
        <div class="textbook-info">
          <h4>教材信息</h4>
          <p><strong>教材名称：</strong>{{ currentTextbook?.name }}</p>
          <p><strong>单词数量：</strong>{{ currentTextbook?.statWordCnt }}个</p>
        </div>

        <el-form :model="downloadAudioForm" label-width="100px">
          <el-form-item label="下载方式">
            <el-radio-group v-model="downloadAudioForm.downloadType">
              <el-radio label="whole">整本下载</el-radio>
              <el-radio label="range">自选范围</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            v-if="downloadAudioForm.downloadType === 'range'"
            label="单词范围"
          >
            <div class="range-inputs">
              <span>从第</span>
              <el-input-number
                v-model="downloadAudioForm.startWordIndex"
                :min="1"
                :max="currentTextbook?.statWordCnt || 1"
                size="small"
                style="width: 80px; margin: 0 8px;"
              />
              <span>个单词到第</span>
              <el-input-number
                v-model="downloadAudioForm.endWordIndex"
                :min="downloadAudioForm.startWordIndex || 1"
                :max="currentTextbook?.statWordCnt || 1"
                size="small"
                style="width: 80px; margin: 0 8px;"
              />
              <span>个单词</span>
            </div>
          </el-form-item>
        </el-form>

        <div class="audio-info">
          <p style="color: #909399; font-size: 14px;">
            <i class="el-icon-info"></i>
            音频包将包含单词音频和例句音频两个目录，文件名格式为"序号.单词.mp3"和"序号.例句.mp3"
          </p>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelDownloadAudio">取消</el-button>
          <el-button
            type="primary"
            @click="confirmDownloadAudio"
            :loading="downloadAudioLoading"
          >
            确定下载
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 下载练习对话框 -->
    <el-dialog
      v-model="downloadExerciseDialogVisible"
      title="下载练习"
      width="500px"
      :before-close="cancelDownloadExercise"
    >
      <div class="download-content">
        <div class="textbook-info">
          <p><strong>教材名称：</strong>{{ currentTextbook?.name }}</p>
          <p><strong>单词总数：</strong>{{ currentTextbook?.statWordCnt }}</p>
        </div>

        <el-form :model="downloadExerciseForm" label-width="100px">
          <el-form-item label="下载方式">
            <el-radio-group v-model="downloadExerciseForm.downloadType">
              <el-radio label="whole">整本下载</el-radio>
              <el-radio label="range">自选范围</el-radio>
            </el-radio-group>
          </el-form-item>

          <div v-if="downloadExerciseForm.downloadType === 'range'">
            <el-form-item label="开始单词">
              <el-input-number
                v-model="downloadExerciseForm.startWordIndex"
                :min="1"
                :max="Number(currentTextbook?.statWordCnt) || 1"
                placeholder="请输入开始单词序号"
              />
            </el-form-item>
            <el-form-item label="结束单词">
              <el-input-number
                v-model="downloadExerciseForm.endWordIndex"
                :min="downloadExerciseForm.startWordIndex"
                :max="Number(currentTextbook?.statWordCnt) || 1"
                placeholder="请输入结束单词序号"
              />
            </el-form-item>
          </div>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelDownloadExercise">取消</el-button>
          <el-button
            type="primary"
            @click="confirmDownloadExercise"
            :loading="downloadExerciseLoading"
          >
            确定下载
          </el-button>
        </span>
      </template>
    </el-dialog>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="handlePagination"
    />
  </div>
</template>

<script setup lang="ts">
import {
  Textbook,
  addOrUpdateTextbook,
  getTextbookList,
  removeTextbook,
  downloadTextbookHandout,
  downloadTextbookAudio,
  downloadTextbookExercise,
  TextbookDownloadReq,
  TextbookDownloadAudioReq,
  TextbookDownloadExerciseReq,
} from "../../../api/textbook";
import { ref, reactive, onMounted } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { ElMessage, UploadFile } from "element-plus";

const searchKeyword = ref("");
const handleSearch = () => {
  // 在这里添加搜索逻辑
  console.log("搜索关键字:", searchKeyword.value);
  queryParams.searchName = searchKeyword.value;
  fetchTextbookList();
};
const handleEdit = (row: Textbook) => {
  dialogVisible.value = true;
  // 重置表单
  Object.assign(form.value, row);
  imageUrl.value = row.cover;
  form.value.content = row.wordList;
};

// 表格数据
const tableData = ref<Textbook[]>([]);
const deletedIdList = ref<string[]>([]);

// 分页参数
const total = ref(0);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 15,
  searchName: "",
});

// 获取教材列表
const fetchTextbookList = async () => {
  try {
    const res = await getTextbookList(queryParams);
    if (res.code === 200) {
      tableData.value = res.data.records;
      total.value = res.data.total;

      tableData.value.forEach((item) => {
        item.gradeName =
          gradeOptions.find((grade) => grade.value === item.grade)?.label || "";
        item.semesterName =
          semesterOptions.find((semester) => semester.value === item.semester)
            ?.label || "";
        item.typeName =
          dictTypeOptions.find((type) => type.value === item.type)?.label || "";
      });
    }
  } catch (error) {
    console.error("获取教材列表失败:", error);
  }
};

// 处理分页变化
const handlePagination = (pagination: any) => {
  queryParams.pageNum = pagination.page;
  queryParams.pageSize = pagination.limit;
  fetchTextbookList();
};

// 页面加载时获取教材列表
onMounted(() => {
  fetchTextbookList();
});

// 弹窗控制
const dialogVisible = ref(false);

// 下载讲义相关
const downloadDialogVisible = ref(false);
const downloadLoading = ref(false);
const currentTextbook = ref<Textbook | null>(null);
const downloadForm = reactive({
  downloadType: 'whole' as 'whole' | 'range' | 'selected',
  startWordIndex: 1,
  endWordIndex: 1,
});

// 下载音频相关
const downloadAudioDialogVisible = ref(false);
const downloadAudioLoading = ref(false);
const downloadAudioForm = reactive({
  downloadType: 'whole' as 'whole' | 'range' | 'selected',
  startWordIndex: 1,
  endWordIndex: 1,
});

// 下载练习相关
const downloadExerciseDialogVisible = ref(false);
const downloadExerciseLoading = ref(false);
const downloadExerciseForm = reactive({
  downloadType: 'whole' as 'whole' | 'range' | 'selected',
  startWordIndex: 1,
  endWordIndex: 1,
});

// 封面图片URL
const imageUrl = ref("");

// 表单数据
const form = ref({
  id: "",
  name: "",
  type: "",
  // tags: '',
  content: "",
  publisher: "",
  grade: null,
  semester: null,
  required: "",
  stage: "",
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: "请输入教材名称", trigger: "blur" }],
  type: [{ required: true, message: "请选择教材类型", trigger: "change" }],
  content: [{ required: true, message: "请输入教材内容", trigger: "blur" }],
  stage: [{ required: true, message: "请选择阶段", trigger: "change" }],
  grade: [{ required: true, message: "请选择年级", trigger: "change" }],
  semester: [{ required: true, message: "请选择学期", trigger: "change" }],
  publisher: [{ required: true, message: "请输入版本", trigger: "blur" }],
};

// 表单引用
const formRef = ref();
const imageFile = ref<UploadFile | null>(null);

// 教材类型选项
const dictTypeOptions = [
  { value: "1", label: "学校教材" },
  { value: "2", label: "特色教材" },
  { value: "3", label: "个性化教材" },
];

const gradeOptions = [
  { value: 1, label: "一年级" },
  { value: 2, label: "二年级" },
  { value: 3, label: "三年级" },
  { value: 4, label: "四年级" },
  { value: 5, label: "五年级" },
  { value: 6, label: "六年级" },
  { value: 7, label: "初一" },
  { value: 8, label: "初二" },
  { value: 9, label: "初三" },
  { value: 10, label: "高一" },
  { value: 11, label: "高二" },
  { value: 12, label: "高三" },
];

const semesterOptions = [
  { value: 1, label: "上学期" },
  { value: 2, label: "下学期" },
  { value: 3, label: "全年" },
];

const stageOptions = [
  { value: "小学", label: "小学" },
  { value: "初中", label: "初中" },
  { value: "高中", label: "高中" },
];

// 处理封面图片上传
const handleCoverChange = (file: any) => {
  const isImage = file.raw.type.startsWith("image/");
  if (!isImage) {
    ElMessage.error("只能上传图片文件!");
    return;
  }

  // 创建临时URL用于预览
  imageUrl.value = URL.createObjectURL(file.raw);
  imageFile.value = file;
};

// 打开新增对话框
const handleAdd = () => {
  dialogVisible.value = true;
  // 重置表单
  form.value.id = "";
  form.value.name = "";
  form.value.type = "";
  // form.tags = '';
  form.value.content = "";
  form.value.publisher = "";
  form.value.grade = null;
  form.value.semester = null;
  form.value.required = "";
  form.value.stage = "";
  imageUrl.value = "";
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        // 处理表单数据
        const formData = new FormData();

        formData.append("name", form.value.name);
        formData.append("type", form.value.type);
        // formData.append('tags', form.tags);
        formData.append("wordList", form.value.content);
        if (imageFile.value) {
          formData.append("coverFile", imageFile.value?.raw || "");
        }
        formData.append("publisher", form.value.publisher);
        formData.append("grade", form.value.grade + "");
        formData.append("semester", form.value.semester + "");
        formData.append("required", form.value.required);
        formData.append("stage", form.value.stage);
        formData.append("id", form.value.id);
        // 调用API提交数据
        const res = await addOrUpdateTextbook(formData);

        if (res.code === 200) {
          ElMessage.success("教材添加成功");
          dialogVisible.value = false;
          // 刷新教材列表
          fetchTextbookList();
        } else {
          ElMessage.error(res.msg || "添加失败");
        }
      } catch (error) {
        console.error("提交表单失败:", error);
        ElMessage.error("提交失败，请稍后重试");
      }
    } else {
      ElMessage.warning("请完善表单信息");
      return false;
    }
  });
};

// 取消提交
const cancelForm = () => {
  dialogVisible.value = false;
};

function deletedText(textbookId: string) {
  deletedIdList.value = [];
  deletedIdList.value.push(textbookId);

  removeTextbook(deletedIdList.value).then(() => {
    ElMessage.success("删除成功");
    fetchTextbookList();
  });
}

// 处理下载讲义
const handleDownloadHandout = (textbook: Textbook) => {
  currentTextbook.value = textbook;
  downloadForm.downloadType = 'whole';
  downloadForm.startWordIndex = 1;
  downloadForm.endWordIndex = Number(textbook.statWordCnt) || 1;
  downloadDialogVisible.value = true;
};

// 处理下载音频
const handleDownloadAudio = (textbook: Textbook) => {
  currentTextbook.value = textbook;
  downloadAudioForm.downloadType = 'whole';
  downloadAudioForm.startWordIndex = 1;
  downloadAudioForm.endWordIndex = Number(textbook.statWordCnt) || 1;
  downloadAudioDialogVisible.value = true;
};

// 处理下载练习
const handleDownloadExercise = (textbook: Textbook) => {
  currentTextbook.value = textbook;
  downloadExerciseForm.downloadType = 'whole';
  downloadExerciseForm.startWordIndex = 1;
  downloadExerciseForm.endWordIndex = Number(textbook.statWordCnt) || 1;
  downloadExerciseDialogVisible.value = true;
};

// 取消下载
const cancelDownload = () => {
  downloadDialogVisible.value = false;
  currentTextbook.value = null;
};

// 取消下载音频
const cancelDownloadAudio = () => {
  downloadAudioDialogVisible.value = false;
  currentTextbook.value = null;
};

// 取消下载练习
const cancelDownloadExercise = () => {
  downloadExerciseDialogVisible.value = false;
  currentTextbook.value = null;
};

// 确认下载
const confirmDownload = async () => {
  if (!currentTextbook.value) {
    ElMessage.error("请选择教材");
    return;
  }

  // 验证范围下载的参数
  if (downloadForm.downloadType === 'range') {
    if (!downloadForm.startWordIndex || !downloadForm.endWordIndex) {
      ElMessage.error("请输入有效的单词范围");
      return;
    }
    if (downloadForm.startWordIndex > downloadForm.endWordIndex) {
      ElMessage.error("开始单词序号不能大于结束单词序号");
      return;
    }
    const totalWords = Number(currentTextbook.value.statWordCnt) || 0;
    if (downloadForm.endWordIndex > totalWords) {
      ElMessage.error(`结束单词序号不能超过教材总单词数(${totalWords})`);
      return;
    }
  }

  downloadLoading.value = true;

  try {
    const downloadReq: TextbookDownloadReq = {
      textbookId: currentTextbook.value.id,
      downloadType: downloadForm.downloadType,
    };

    if (downloadForm.downloadType === 'range') {
      downloadReq.startWordIndex = downloadForm.startWordIndex;
      downloadReq.endWordIndex = downloadForm.endWordIndex;
    }

    const res = await downloadTextbookHandout(downloadReq);

    if (res.code === 200) {
      // 处理下载响应
      const { body, headers } = res.data;

      // Base64转Blob
      const byteCharacters = atob(body);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: headers["Content-Type"][0] });

      // 获取文件名
      const contentDisposition = headers["Content-Disposition"][0];
      const fileName = contentDisposition.match(/filename="(.+)"/)?.[1] || "讲义.pdf";

      // 创建下载链接
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);

      ElMessage.success("下载成功");
      downloadDialogVisible.value = false;
    } else {
      ElMessage.error("下载失败：" + res.message);
    }
  } catch (error) {
    console.error("下载讲义失败:", error);
    ElMessage.error("下载失败，请稍后重试");
  } finally {
    downloadLoading.value = false;
  }
};

// 确认下载音频
const confirmDownloadAudio = async () => {
  if (!currentTextbook.value) {
    ElMessage.error("请选择教材");
    return;
  }

  // 验证范围下载的参数
  if (downloadAudioForm.downloadType === 'range') {
    if (!downloadAudioForm.startWordIndex || !downloadAudioForm.endWordIndex) {
      ElMessage.error("请输入有效的单词范围");
      return;
    }
    if (downloadAudioForm.startWordIndex > downloadAudioForm.endWordIndex) {
      ElMessage.error("开始单词序号不能大于结束单词序号");
      return;
    }
    const totalWords = Number(currentTextbook.value.statWordCnt) || 0;
    if (downloadAudioForm.endWordIndex > totalWords) {
      ElMessage.error(`结束单词序号不能超过教材总单词数(${totalWords})`);
      return;
    }
  }

  downloadAudioLoading.value = true;

  try {
    const downloadReq: TextbookDownloadAudioReq = {
      textbookId: currentTextbook.value.id,
      downloadType: downloadAudioForm.downloadType,
    };

    if (downloadAudioForm.downloadType === 'range') {
      downloadReq.startWordIndex = downloadAudioForm.startWordIndex;
      downloadReq.endWordIndex = downloadAudioForm.endWordIndex;
    }

    const res = await downloadTextbookAudio(downloadReq);

    if (res.code === 200) {
      // 处理下载响应 - 现在返回的是下载链接
      const { downloadUrl } = res.data;

      if (downloadUrl) {
        // 打开新标签页触发下载
        window.open(downloadUrl, '_blank');
        ElMessage.success("下载链接已生成，正在下载...");
        downloadAudioDialogVisible.value = false;
      } else {
        ElMessage.error("下载链接生成失败");
      }
    } else {
      ElMessage.error("下载失败：" + res.message);
    }
  } catch (error) {
    console.error("下载音频失败:", error);
    ElMessage.error("下载失败，请稍后重试");
  } finally {
    downloadAudioLoading.value = false;
  }
};

// 确认下载练习
const confirmDownloadExercise = async () => {
  if (!currentTextbook.value) {
    ElMessage.error("请选择教材");
    return;
  }

  // 验证范围下载的参数
  if (downloadExerciseForm.downloadType === 'range') {
    if (!downloadExerciseForm.startWordIndex || !downloadExerciseForm.endWordIndex) {
      ElMessage.error("请输入有效的单词范围");
      return;
    }
    if (downloadExerciseForm.startWordIndex > downloadExerciseForm.endWordIndex) {
      ElMessage.error("开始单词序号不能大于结束单词序号");
      return;
    }
    const totalWords = Number(currentTextbook.value.statWordCnt) || 0;
    if (downloadExerciseForm.endWordIndex > totalWords) {
      ElMessage.error(`结束单词序号不能超过教材总单词数(${totalWords})`);
      return;
    }
  }

  downloadExerciseLoading.value = true;

  try {
    const downloadReq: TextbookDownloadExerciseReq = {
      textbookId: currentTextbook.value.id,
      downloadType: downloadExerciseForm.downloadType,
    };

    if (downloadExerciseForm.downloadType === 'range') {
      downloadReq.startWordIndex = downloadExerciseForm.startWordIndex;
      downloadReq.endWordIndex = downloadExerciseForm.endWordIndex;
    }

    const res = await downloadTextbookExercise(downloadReq);

    if (res.code === 200) {
      // 处理下载响应
      const { body, headers } = res.data;

      // Base64转Blob
      const byteCharacters = atob(body);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: headers["Content-Type"][0] });

      // 获取文件名
      const contentDisposition = headers["Content-Disposition"][0];
      const fileNameMatch = contentDisposition.match(/filename="(.+)"/);
      const fileName = fileNameMatch ? fileNameMatch[1] : "练习.pdf";

      // 创建下载链接
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);

      ElMessage.success("练习下载成功");
      downloadExerciseDialogVisible.value = false;
    } else {
      ElMessage.error("下载失败：" + res.message);
    }
  } catch (error) {
    console.error("下载练习失败:", error);
    ElMessage.error("下载失败，请稍后重试");
  } finally {
    downloadExerciseLoading.value = false;
  }
};
</script>
<style lang="css">
.main-class {
  margin: 10px;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.dialog-top {
  display: flex;
  gap: 20px;
}

.cover-upload {
  width: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 178px;
  height: 178px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-uploader:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.upload-text {
  margin-top: 10px;
  color: #606266;
}

.form-section {
  flex: 1;
}

.content-label {
  display: flex;
  align-items: center;
  font-weight: bold;
  margin-bottom: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.searchClass {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.searchItem {
  margin-right: 20px;
}

/* 下载讲义对话框样式 */
.download-content {
  padding: 10px 0;
}

.textbook-info {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.textbook-info h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 16px;
}

.textbook-info p {
  margin: 5px 0;
  color: #606266;
  font-size: 14px;
}

.range-inputs {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 音频下载提示样式 */
.audio-info {
  background-color: #f0f9ff;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #409eff;
  margin-top: 15px;
}

.audio-info p {
  margin: 0;
  line-height: 1.5;
}
</style>
